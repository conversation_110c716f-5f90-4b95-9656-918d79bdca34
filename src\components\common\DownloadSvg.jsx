import React from 'react'

const DownloadSvg = () => {
    return (
        <svg className='inline-block align-middle' width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21.75 15C21.4186 15.0005 21.101 15.1324 20.8667 15.3667C20.6324 15.601 20.5005 15.9186 20.5 16.25V19.25C20.4995 19.4488 20.4203 19.6392 20.2797 19.7797C20.1392 19.9203 19.9487 19.9995 19.75 20H3.75C3.55125 19.9995 3.36079 19.9203 3.22025 19.7797C3.07972 19.6392 3.00053 19.4488 3 19.25V16.25C3 15.9185 2.8683 15.6005 2.63388 15.3661C2.39946 15.1317 2.08152 15 1.75 15C1.41848 15 1.10054 15.1317 0.866117 15.3661C0.631696 15.6005 0.5 15.9185 0.5 16.25V19.25C0.501059 20.1116 0.843808 20.9377 1.45307 21.5469C2.06234 22.1562 2.88837 22.4989 3.75 22.5H19.75C20.6116 22.4989 21.4377 22.1562 22.0469 21.5469C22.6562 20.9377 22.9989 20.1116 23 19.25V16.25C22.9995 15.9186 22.8676 15.601 22.6333 15.3667C22.399 15.1324 22.0814 15.0005 21.75 15Z" fill="currentColor" />
            <path d="M11.7491 0C11.4177 0.000528905 11.1001 0.132395 10.8658 0.366701C10.6315 0.601007 10.4996 0.918642 10.4991 1.25V12.581L6.54909 9.29C6.29434 9.07817 5.96596 8.97606 5.636 9.00605C5.30605 9.03605 5.00147 9.19571 4.78909 9.45C4.57726 9.70475 4.47515 10.0331 4.50514 10.3631C4.53514 10.693 4.6948 10.9976 4.94909 11.21L10.9491 16.21C11.0121 16.2547 11.079 16.2935 11.1491 16.326C11.1948 16.3566 11.2426 16.384 11.2921 16.408C11.4363 16.4687 11.5911 16.5 11.7476 16.5C11.904 16.5 12.0589 16.4687 12.2031 16.408C12.2514 16.3839 12.2981 16.3569 12.3431 16.327C12.4131 16.2945 12.4801 16.2556 12.5431 16.211L18.5431 11.211C18.7403 11.047 18.8823 10.8262 18.9497 10.5787C19.0172 10.3312 19.0069 10.0689 18.9202 9.82751C18.8335 9.58608 18.6746 9.37717 18.4651 9.22913C18.2556 9.08109 18.0056 9.0011 17.7491 9C17.4568 9.00055 17.1738 9.10312 16.9491 9.29L12.9991 12.581V1.25C12.9986 0.918642 12.8667 0.601007 12.6324 0.366701C12.3981 0.132395 12.0804 0.000528905 11.7491 0Z" fill="currentColor" />
        </svg>

    )
}

export default DownloadSvg