{"name": "animate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.2", "@splidejs/react-splide": "^0.7.12", "@splidejs/splide": "^4.1.4", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "@tsparticles/react": "^3.0.0", "gsap": "^3.13.0", "gsap-trial": "^3.12.7", "magnet-mouse": "github:fluffy-factory/magnet-mouse", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.2", "react-tsparticles": "^2.12.2", "tsparticles": "^3.8.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/vite": "^4.1.4", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}