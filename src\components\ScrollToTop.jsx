import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { ScrollSmoother } from 'gsap/ScrollSmoother';

const ScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    // Function to scroll to top
    const scrollToTop = () => {
      const smoother = ScrollSmoother.get();
      
      if (smoother) {
        // Use ScrollSmoother's scrollTo method for smooth scrolling
        smoother.scrollTo(0, true);
      } else {
        // Fallback to regular window scroll if ScrollSmoother is not available
        window.scrollTo(0, 0);
      }
    };

    // Small delay to ensure the page has rendered
    const timeoutId = setTimeout(scrollToTop, 100);

    return () => clearTimeout(timeoutId);
  }, [location.pathname]); // Trigger when route changes

  return null; // This component doesn't render anything
};

export default ScrollToTop;
